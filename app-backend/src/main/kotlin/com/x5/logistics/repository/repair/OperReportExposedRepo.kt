package com.x5.logistics.repository.repair

import com.x5.logistics.data.OperationalReport
import com.x5.logistics.data.dictionary.OrganizationalUnitsTimelineTable
import com.x5.logistics.repository.applyGeoFilter
import com.x5.logistics.repository.buildFilterPredicate
import com.x5.logistics.rest.dto.operreport.OperReportColumn
import com.x5.logistics.rest.dto.operreport.OperReportRequest
import com.x5.logistics.util.getLogger
import org.jetbrains.exposed.sql.ExpressionAlias
import org.jetbrains.exposed.sql.JoinType
import org.jetbrains.exposed.sql.Query
import org.jetbrains.exposed.sql.SortOrder
import org.jetbrains.exposed.sql.alias
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.andHaving
import org.jetbrains.exposed.sql.andWhere
import org.springframework.stereotype.Component

@Component
class OperReportExposedRepo {

    val logger = getLogger()

    suspend fun prepareBaseQuery(
        req: OperReportRequest,
        filterValuesAgg: ExpressionAlias<String>? = null,
        requestedColumn: OperReportColumn? = null
    ): Query {
        req.columns
        val (aggColumns, regularColumns) = req.columns.partition { it.isAggregated }

        val (aggFilters, regularFilters) = req.filters.partition { it.name.isAggregated }

        val columnsForCalculation = listOf(
            OperReportColumn.periodDay.exposedExpression,
            OperReportColumn.vehicleLicense.exposedExpression,
            OperationalReport.kip,
            OperationalReport.rg,
            OperationalReport.ktg,
            OperationalReport.kipNoReserve,
            OperationalReport.numTripTms,
            OperationalReport.statusTFinal,
            OperationalReport.statusTCalculate,
            OperationalReport.isDelvWndowCount,
            OperationalReport.pointsCount,
            OperationalReport.isDelvIntimeCount,
            OperationalReport.isDelvWindowWithPlanWndowCount,
            OperationalReport.carIn,
            OperationalReport.ourTripFlag,
            OperReportColumn.ourTripText.exposedExpression,
            OperationalReport.carInGps,
            OperationalReport.comeOff
        )
        val subquery = with(OperationalReport) {
            join(OrganizationalUnitsTimelineTable, JoinType.INNER) {
                (OrganizationalUnitsTimelineTable.mvzId eq mvzId) and
                        (OrganizationalUnitsTimelineTable.startDate lessEq vehicleDate) and
                        (OrganizationalUnitsTimelineTable.endDate greater vehicleDate)
            }.select(
                (regularColumns.map { it.exposedExpression } + columnsForCalculation + listOfNotNull(requestedColumn?.exposedExpression))
                    .distinct() + listOf(rowNumber)
            ).where {
                vehicleDate.between(req.from, req.to)
            }.applyGeoFilter(req.geoFilter)
                .apply {
                    regularFilters.forEach {
                        andWhere {
                            buildFilterPredicate(
                                it.condition,
                                it.value,
                                it.name.type,
                                it.name.exposedExpression.delegate,
                                true,
                                it.name.precision
                            )
                        }
                    }
                }
        }.alias("subquery")

        val rep = with(OperationalReport) {
            subquery.select(
                if (filterValuesAgg == null) {
                    regularColumns.map { it.exposedExpression.aliasOnlyExpression() } +
                            aggColumns.map { it.exposedExpression } + count
                } else {
                    listOfNotNull(filterValuesAgg)
                }
            ).groupBy(
                *(regularColumns.map {
                    it.exposedExpression.aliasOnlyExpression()
                }.toTypedArray())
            ).apply {
                aggFilters.forEach {
                    andHaving {
                        val exp = it.name.exposedExpression.delegate
                        buildFilterPredicate(it.condition, it.value, it.name.type, exp, precision = it.name.precision)
                    }
                }
            }.apply {
                req.sort.forEach { sort ->
                    val exp =
                        sort.column.exposedExpression.aliasOnlyExpression()
                    orderBy(
                        exp, if (sort.asc) SortOrder.ASC else SortOrder.DESC
                    )
                }
            }.apply {
                if (req.pageSize != null && req.pageNumber != null) {
                    limit(req.pageSize, offset = (req.pageNumber * req.pageSize).toLong())
                }
            }
        }
        return rep
    }
}