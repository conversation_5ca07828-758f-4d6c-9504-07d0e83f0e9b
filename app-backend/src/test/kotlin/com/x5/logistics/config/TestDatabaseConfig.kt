package com.x5.logistics.config

import com.x5.logistics.TestPostgresqlConfig
import org.jetbrains.exposed.sql.Database
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary
import org.springframework.beans.factory.annotation.Qualifier
import javax.sql.DataSource
import com.zaxxer.hikari.HikariDataSource

@TestConfiguration(proxyBeanMethods = false)
class TestDatabaseConfig {

    @Bean("testDataSource")
    @Primary
    fun testDataSource(): DataSource {
        return HikariDataSource().apply {
            jdbcUrl = TestPostgresqlConfig.container.jdbcUrl
            username = TestPostgresqlConfig.container.username
            password = TestPostgresqlConfig.container.password
            driverClassName = "org.postgresql.Driver"
            maximumPoolSize = 5
        }
    }

    @Bean("database")
    @Primary
    fun testDatabase(@Qualifier("testDataSource") testDataSource: DataSource): Database {
        return Database.connect(testDataSource)
    }
}