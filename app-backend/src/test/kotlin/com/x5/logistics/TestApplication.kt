package com.x5.logistics

import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.autoconfigure.liquibase.LiquibaseAutoConfiguration
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.context.annotation.ComponentScan
import org.springframework.context.annotation.FilterType

@SpringBootApplication(
    exclude = [
        LiquibaseAutoConfiguration::class,
        org.jetbrains.exposed.spring.autoconfigure.ExposedAutoConfiguration::class
    ]
)
@ComponentScan(
    basePackages = ["com.x5.logistics"],
    excludeFilters = [
        ComponentScan.Filter(
            type = FilterType.ASSIGNABLE_TYPE,
            classes = [
                com.x5.logistics.config.SpringTransactions::class,
                com.x5.logistics.config.ExposedConfig::class,
                com.x5.logistics.config.DataBaseStartUpConfig::class,
                com.x5.logistics.config.DataSourceProxyConfiguration::class
            ]
        )
    ]
)
@EnableConfigurationProperties(DataBaseConfigurations::class)
class TestApplication
