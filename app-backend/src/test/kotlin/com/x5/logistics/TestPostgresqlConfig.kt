package com.x5.logistics

import com.x5.logistics.rest.controller.DictionaryController
import org.junit.jupiter.api.BeforeAll
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.boot.test.util.TestPropertyValues
import org.springframework.context.ApplicationContextInitializer
import org.springframework.context.ConfigurableApplicationContext
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.context.TestPropertySource
import org.testcontainers.containers.PostgreSQLContainer
import org.testcontainers.junit.jupiter.Testcontainers

@SpringBootTest
@Testcontainers
@ContextConfiguration(initializers = [TestPostgresqlInitializer::class])
@TestPropertySource(value = ["classpath:application-test.properties"])
abstract class TestPostgresqlConfig {
    //exclude from context scheduled operation with no DB setup for it
    @MockBean
    private lateinit var dict: Dictionary<PERSON><PERSON>roller

    @MockBean
    private lateinit var dataBaseStartUpConfig: com.x5.logistics.config.DataBaseStartUpConfig

    companion object {
        @JvmStatic
        val container: PostgreSQLContainer<*> = PostgreSQLContainer("postgres:16-alpine")
            .withDatabaseName("transport_test_db")
            .withUsername("stv_bd_avto")
            .withPassword("test_password") // Add a default test password
            .withUrlParam("currentSchema", "ts")
            .withUrlParam("stringtype", "unspecified")
            .withInitScript("before.sql")
            .withReuse(true)
            .withStartupTimeout(java.time.Duration.ofSeconds(120))

        @JvmStatic
        @BeforeAll
        fun setupSchema() {
            if (!container.isRunning) {
                container.start()
            }
            ActiveDB.name = "test"
        }
    }
}

class TestPostgresqlInitializer : ApplicationContextInitializer<ConfigurableApplicationContext> {
    override fun initialize(applicationContext: ConfigurableApplicationContext) {
        TestPropertyValues.of(
            "spring.datasource.url=${TestPostgresqlConfig.container.jdbcUrl}",
            "spring.datasource.username=${TestPostgresqlConfig.container.username}",
            "spring.datasource.password=${TestPostgresqlConfig.container.password}",
            "spring.datasource.driver-class-name=org.postgresql.Driver",
            "spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect",
            // Настройка для тестов - используем testcontainers как единственную БД
            "db.configurations.test.url=${TestPostgresqlConfig.container.jdbcUrl}",
            "db.configurations.test.username=${TestPostgresqlConfig.container.username}",
            "db.configurations.test.password=${TestPostgresqlConfig.container.password}"
        ).applyTo(applicationContext)
    }
}